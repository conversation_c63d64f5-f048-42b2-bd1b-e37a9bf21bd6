import {
  BLUR_LEVEL_COEFFS,
  convertPolyToScalable,
  OBJECT_TYPE,
  REDACTION_CODE_FONT_SCALING_COEFF,
  REDACTION_CODE_FONT_SIZE,
  REDACTION_CODE_MIN_FONT_PX,
} from '@helpers/constants';
import {
  GlobalSettings,
  HighlightedOverlay,
  RedactionType,
  ShapeType,
} from '@common-modules/mediaDetails/models';
import { GroupedBoundingPoly } from '@worker';
import { percentagePolyToPixelXYWidthHeight } from './helpers';
import { IndividualRedactionCode } from '@redact-modules/redactionCodes/models';

interface BlurPoly {
  id: string;
  x: number;
  y: number;
  blurPx: number;
  polyWidth: number;
  polyHeight: number;
  shapeType: ShapeType;
  redactionCode?: IndividualRedactionCode;
}

const outlineStyles = {
  udr: '255, 235, 59',
  head: '0, 160, 119',
  licensePlate: '0, 183, 236',
  laptop: '243, 100, 13',
  poim: '201, 51, 242',
  notepad: '191, 64, 191',
  vehicle: '0, 183, 236',
  card: '65, 105, 225',
  person: '237, 64, 93',
};

const disabledStyles = {
  border: 'rgba(200, 200, 200)',
  background: 'rgba(80, 80, 80, 0.15)',
};

const setFill = (
  context: CanvasRenderingContext2D,
  overlayPreview: 'black_fill' | 'outline' | 'redacted',
  fillType: RedactionType,
  type: OBJECT_TYPE
) =>
  ({
    black_fill: () => {
      context.fillStyle = 'black';
    },
    outline: () => {
      context.fillStyle = `rgba(${outlineStyles[type]}, 0.15)`;
      context.strokeStyle = `rgb(${outlineStyles[type]})`;
      context.lineWidth = 2;
    },
    redacted: {
      black_fill: () => {
        context.fillStyle = 'black';
      },
      blur: () => {},
      outline: () => {
        context.lineWidth = 3;
        context.strokeStyle = 'white';
        context.fillStyle = 'transparent';
      },
    }[fillType],
  })[overlayPreview]();

const drawBoundingBox = (
  context: CanvasRenderingContext2D,
  shapeType: 'rectangle' | 'ellipse',
  x: number,
  y: number,
  polyWidth: number,
  polyHeight: number,
  width: number,
  height: number
) => {
  if (shapeType === 'rectangle') {
    context.rect(x, y, polyWidth, polyHeight);
  } else {
    context.moveTo(x, y + polyHeight / 2);
    if (x <= 0 || y <= 0) {
      context.lineTo(x, y);
      context.lineTo(x + polyWidth / 2, y);
    } else {
      context.bezierCurveTo(x, y + polyHeight / 2, x, y, x + polyWidth / 2, y);
    }
    if (Math.ceil(x) + Math.ceil(polyWidth) >= width || y <= 0) {
      context.lineTo(x + polyWidth, y);
      context.lineTo(x + polyWidth, y + polyHeight / 2);
    } else {
      context.bezierCurveTo(
        x + polyWidth / 2,
        y,
        x + polyWidth,
        y,
        x + polyWidth,
        y + polyHeight / 2
      );
    }
    if (
      Math.ceil(x) + Math.ceil(polyWidth) >= width ||
      Math.ceil(y) + Math.ceil(polyHeight) >= height
    ) {
      context.lineTo(x + polyWidth, y + polyHeight);
      context.lineTo(x + polyWidth / 2, y + polyHeight);
    } else {
      context.bezierCurveTo(
        x + polyWidth,
        y + polyHeight / 2,
        x + polyWidth,
        y + polyHeight,
        x + polyWidth / 2,
        y + polyHeight
      );
    }
    if (x <= 0 || Math.ceil(y) + Math.ceil(polyHeight) >= height) {
      context.lineTo(x, y + polyHeight);
      context.lineTo(x, y + polyHeight / 2);
    } else {
      context.bezierCurveTo(
        x + polyWidth / 2,
        y + polyHeight,
        x,
        y + polyHeight,
        x,
        y + polyHeight / 2
      );
    }
    context.closePath();
  }
};

const drawRedactionCode = (
  context: CanvasRenderingContext2D,
  redactionCode: GroupedBoundingPoly['redactionCode'],
  x: number,
  y: number,
  polyWidth: number,
  polyHeight: number
) => {
  if (redactionCode) {
    const calculatedCqw =
      REDACTION_CODE_FONT_SCALING_COEFF / redactionCode.code.length;
    const calculatedPx = ((calculatedCqw / 100) * polyWidth).toFixed(5);

    const fontSize = `clamp(${REDACTION_CODE_MIN_FONT_PX}, ${calculatedPx}px, ${
      REDACTION_CODE_FONT_SIZE *
      (document.getElementById('overlay-container')?.offsetHeight ?? 1080)
    }px)`;
    context.font = `${fontSize} Roboto`;
    context.textAlign = 'center';
    context.textBaseline = 'middle';
    context.fillStyle = redactionCode.codeColor;

    // Hide overflow text
    context.beginPath();
    context.rect(x, y, polyWidth, polyHeight);
    context.clip();
    context.fillText(redactionCode.code, x + polyWidth / 2, y + polyHeight / 2);
  }
};

const discretize = (polys: BlurPoly[], tolerance: number) => {
  const sortedPolys = polys.sort((a, b) => a.blurPx - b.blurPx);
  if (!sortedPolys[0]) {
    return [];
  }

  let cmin = sortedPolys[0].blurPx;
  let pending: BlurPoly[] = [];
  const clusters: BlurPoly[][] = [];
  for (const s of sortedPolys) {
    const center = (s.blurPx + cmin) / 2;
    const percFromCenter = center / cmin;
    if (percFromCenter - 1 > tolerance) {
      clusters.push(pending);
      pending = [];
      cmin = s.blurPx;
    }
    pending.push(s);
  }
  clusters.push(pending);
  return clusters;
};

export const drawPolys = (
  polys: GroupedBoundingPoly[],
  unselectedPolys: GroupedBoundingPoly[],
  context: CanvasRenderingContext2D,
  overlayPreview: 'black_fill' | 'outline' | 'redacted',
  objectTypeEffects: GlobalSettings['objectTypeEffects'],
  localOverlayBeingUpdated: GroupedBoundingPoly | null,
  highlightedOverlay: HighlightedOverlay | undefined,
  draggedOverlayId: string | null
) => {
  const { width, height } = context.canvas;
  const blurCanvas = new OffscreenCanvas(width, height);
  const blurContext = blurCanvas.getContext('2d', { alpha: false })!;

  const unselectedPolysIds = unselectedPolys?.map(({ id }) => id) ?? [];
  const [blurPolys, otherPolys] = polys.reduce<
    [BlurPoly[], GroupedBoundingPoly[]]
  >(
    ([blurPolys, otherPolys], poly) => {
      const {
        id,
        redactionConfig,
        redactionCode,
        shapeType: polyShapeType,
        type,
      } = poly;
      if (localOverlayBeingUpdated?.id === id) {
        return [blurPolys, otherPolys];
      }

      const convertedType = convertPolyToScalable(type) ?? 'udr';
      const fillType =
        redactionConfig?.fillType ??
        objectTypeEffects[convertedType].redactionConfig.fillType;

      /* Check if NOT Blur */
      if (overlayPreview !== 'redacted' || fillType !== 'blur') {
        otherPolys.push(poly);
        return [blurPolys, otherPolys];
      }

      const {
        x,
        y,
        width: polyWidth,
        height: polyHeight,
      } = percentagePolyToPixelXYWidthHeight(
        poly.boundingPoly,
        width,
        height,
        convertedType !== 'udr' ? objectTypeEffects[convertedType].scaling : 0
      );
      const shapeType =
        polyShapeType ??
        (convertedType
          ? objectTypeEffects[convertedType].shapeType
          : 'rectangle');
      const blurLevel =
        redactionConfig?.blurLevel ??
        objectTypeEffects[convertedType].redactionConfig.blurLevel;
      const blurPx = Math.floor(
        BLUR_LEVEL_COEFFS[blurLevel] * Math.max(polyWidth, polyHeight)
      );

      blurPolys.push({
        id: poly.id,
        x,
        y,
        blurPx,
        polyWidth,
        polyHeight,
        shapeType,
        redactionCode,
      });
      return [blurPolys, otherPolys];
    },
    [[], [...unselectedPolys]]
  );
  const discretizedBlurPolys = discretize(blurPolys, 0.3);

  /* Draw Blurred Polys */
  for (const blurGroup of discretizedBlurPolys) {
    // render the box if it is not being dragged only
    const averageBlur =
      blurGroup.reduce((acc, { blurPx }) => acc + blurPx, 0) / blurGroup.length;

    context.save();
    context.beginPath();
    blurContext.filter = `blur(${averageBlur}px)`;
    blurContext.drawImage(context.canvas, 0, 0, width, height);
    for (const { polyWidth, polyHeight, x, y, shapeType, id } of blurGroup) {
      if (id !== draggedOverlayId) {
        drawBoundingBox(
          context,
          shapeType,
          x,
          y,
          polyWidth,
          polyHeight,
          width,
          height
        );
      }
    }
    context.clip();

    context.drawImage(
      blurContext.canvas,
      0,
      0,
      width,
      height,
      0,
      0,
      width,
      height
    );
    context.restore();

    /* Draw Blurred Poly Redaction Codes */
    for (const {
      polyWidth,
      polyHeight,
      x,
      y,
      redactionCode,
      id,
    } of blurGroup) {
      context.save();
      context.beginPath();
      if (id !== draggedOverlayId) {
        drawRedactionCode(context, redactionCode, x, y, polyWidth, polyHeight);
      }
      context.restore();
    }
  }

  /* Draw All Other Polys */
  for (const {
    id,
    boundingPoly,
    redactionCode,
    redactionConfig,
    shapeType: polyShapeType,
    type,
  } of otherPolys) {
    const convertedType = convertPolyToScalable(type) ?? 'udr';
    const shapeType =
      polyShapeType ??
      (convertedType
        ? objectTypeEffects[convertedType].shapeType
        : 'rectangle');

    const {
      x,
      y,
      width: polyWidth,
      height: polyHeight,
    } = percentagePolyToPixelXYWidthHeight(
      boundingPoly,
      width,
      height,
      convertedType !== 'udr' ? objectTypeEffects[convertedType].scaling : 0
    );

    context.save();
    context.beginPath();

    const fillType =
      redactionConfig?.fillType ??
      objectTypeEffects[convertedType].redactionConfig.fillType;
    const isUnselected = unselectedPolysIds.includes(id);

    if (isUnselected) {
      context.fillStyle = disabledStyles.background;
      context.strokeStyle = disabledStyles.border;
      context.setLineDash([3, 2]);
      context.lineWidth = 0.5;
    } else {
      setFill(context, overlayPreview, fillType, type);
    }
    if (id !== draggedOverlayId) {
      // render the box if it is not being dragged only
      drawBoundingBox(
        context,
        shapeType,
        x,
        y,
        polyWidth,
        polyHeight,
        width,
        height
      );
      context.fill();
      context.stroke();
      if (!isUnselected) {
        drawRedactionCode(context, redactionCode, x, y, polyWidth, polyHeight);
      }
    }

    context.restore();
  }

  /* Add resize markers indicate highlightedOverlay */
  if (highlightedOverlay) {
    const highlightedPoly = polys.find(
      (poly) =>
        !!highlightedOverlay.groupId &&
        poly.groupId === highlightedOverlay.groupId &&
        localOverlayBeingUpdated?.id !== poly.id
    );

    if (highlightedPoly) {
      const convertedType =
        convertPolyToScalable(highlightedPoly.type) ?? 'udr';

      const size = percentagePolyToPixelXYWidthHeight(
        highlightedPoly.boundingPoly,
        width,
        height,
        convertedType !== 'udr' ? objectTypeEffects[convertedType].scaling : 0
      );
      context.save();
      context.beginPath();
      context.fillStyle = 'white';
      const markerSize = 6;

      const { width: polyWidth, height: polyHeight } = size;

      const x = size.x - markerSize / 2; // center align
      const y = size.y - markerSize / 2; // center align

      context.rect(x, y, markerSize, markerSize); // upper left
      context.rect(x + polyWidth, y, markerSize, markerSize); // upper right
      context.rect(x + polyWidth, y + polyHeight, markerSize, markerSize); // lower right
      context.rect(x, y + polyHeight, markerSize, markerSize); // lower left

      context.rect(x + polyWidth / 2, y, markerSize, markerSize); // top
      context.rect(x + polyWidth, y + polyHeight / 2, markerSize, markerSize); // right
      context.rect(x, y + polyHeight / 2, markerSize, markerSize); // left
      context.rect(x + polyWidth / 2, y + polyHeight, markerSize, markerSize); // bottom
      context.fill();
      context.restore();
    }
  }
};
